# 地面间隙微调实现

## 📋 需求
在模型正确贴紧地面的基础上，需要微调让所有模型的底部距离地面0.2个单位。

## ✅ 实现方案

### 1. 添加地面间隙常量
```javascript
// 地面间隙常量 - 模型底部距离地面的固定间隙
const GROUND_CLEARANCE = 0.2;
```

### 2. 修改位置计算逻辑
在原有的地面偏移量基础上，额外添加0.2个单位的间隙：

```javascript
// applyPosition 函数中
if (config.autoGroundPosition !== false) {
  // 将配置中的Y坐标作为地面坐标系的高度，然后加上地面偏移量和地面间隙
  yPosition = yPosition + groundOffset.value + GROUND_CLEARANCE;
}

// setPositionImmediateRaw 函数中
if (props.modelConfig.autoGroundPosition !== false) {
  // 将传入的Y坐标作为地面坐标系的高度，然后加上地面偏移量和地面间隙
  yPosition = yPosition + groundOffset.value + GROUND_CLEARANCE;
}
```

### 3. 更新调试信息
让日志清楚地显示地面间隙的应用：

```javascript
console.log(`模型 ${props.modelConfig.id} 位置变换:`, {
  原始配置: positionConfig,
  地面偏移量: groundOffset.value,
  地面间隙: GROUND_CLEARANCE,
  自动地面定位: config.autoGroundPosition !== false,
  最终世界坐标: newPosition,
  Y坐标计算: `${positionConfig.y || 0} + ${groundOffset.value} + ${GROUND_CLEARANCE} = ${yPosition}`
});
```

## 🎯 效果验证

### 测试结果
- ✅ Y=0 的模型：底部距离地面 0.2 个单位
- ✅ Y=5 的模型：底部距离地面 5.2 个单位  
- ✅ 所有类型的模型（不同缩放、不同边界框）都正确应用间隙
- ✅ 保持了原有的相对高度关系，只是整体抬高了 0.2 个单位

### 数学验证
```
最终Y坐标 = 配置Y + 地面偏移量 + 地面间隙
模型底部实际位置 = 最终Y坐标 + box.min.y
预期距离地面高度 = 配置Y + 0.2

验证：|模型底部实际位置 - 预期距离地面高度| < 0.001 ✅
```

## 🔧 配置说明

### 当前实现
- 所有模型统一使用 `GROUND_CLEARANCE = 0.2` 的间隙
- 间隙值定义为常量，便于统一调整

### 扩展可能性
如果将来需要为不同模型设置不同的间隙，可以考虑：

```javascript
// 在模型配置中添加可选的 groundClearance 属性
const modelGroundClearance = config.groundClearance !== undefined 
  ? config.groundClearance 
  : GROUND_CLEARANCE;

yPosition = yPosition + groundOffset.value + modelGroundClearance;
```

## 📊 修改文件
- `ModelComponent.vue` - 主要的位置计算逻辑

## 🎉 总结
通过添加 `GROUND_CLEARANCE = 0.2` 常量并在位置计算中应用，成功实现了：

1. **精确控制**：所有模型底部精确距离地面0.2个单位
2. **向后兼容**：保持了原有的相对高度关系
3. **易于维护**：通过常量统一管理间隙值
4. **调试友好**：日志清楚显示间隙的应用情况

现在所有加载到场景中的模型都会：
- 正确计算边界框和地面偏移量
- 应用0.2个单位的地面间隙
- 保持配置的相对高度关系
- 在控制台输出详细的位置计算信息
