// 测试缩放对边界框计算的影响
// 这个测试说明了为什么需要在应用缩放后重新计算边界框

console.log('=== 缩放对边界框影响的测试 ===');

// 模拟一个模型的原始边界框
const originalBoundingBox = {
  min: { x: -100, y: -620, z: -100 },  // 原始模型底部在Y=-620
  max: { x: 100, y: 300, z: 100 }      // 原始模型顶部在Y=300
};

console.log('原始边界框:', originalBoundingBox);

// 计算原始地面偏移量
const originalGroundOffset = -originalBoundingBox.min.y;
console.log('原始地面偏移量:', originalGroundOffset);

// 应用缩放 (典型的模型缩放)
const scale = 0.01;
console.log('应用缩放:', scale);

// 缩放后的边界框
const scaledBoundingBox = {
  min: { 
    x: originalBoundingBox.min.x * scale, 
    y: originalBoundingBox.min.y * scale, 
    z: originalBoundingBox.min.z * scale 
  },
  max: { 
    x: originalBoundingBox.max.x * scale, 
    y: originalBoundingBox.max.y * scale, 
    z: originalBoundingBox.max.z * scale 
  }
};

console.log('缩放后边界框:', scaledBoundingBox);

// 计算缩放后的地面偏移量
const scaledGroundOffset = -scaledBoundingBox.min.y;
console.log('缩放后地面偏移量:', scaledGroundOffset);

console.log('\n=== 对比结果 ===');
console.log(`原始地面偏移量: ${originalGroundOffset}`);
console.log(`缩放后地面偏移量: ${scaledGroundOffset}`);
console.log(`差异: ${originalGroundOffset - scaledGroundOffset}`);
console.log(`缩放比例: ${scaledGroundOffset / originalGroundOffset}`);

console.log('\n=== 问题说明 ===');
console.log('如果在缩放前计算边界框:');
console.log(`- 地面偏移量 = ${originalGroundOffset}`);
console.log(`- 模型Y坐标 = 0 + ${originalGroundOffset} = ${originalGroundOffset}`);
console.log(`- 但模型实际缩放后底部在 Y = ${originalGroundOffset} + ${scaledBoundingBox.min.y} = ${originalGroundOffset + scaledBoundingBox.min.y}`);
console.log(`- 结果: 模型悬浮在空中 ${originalGroundOffset + scaledBoundingBox.min.y} 个单位!`);

console.log('\n如果在缩放后计算边界框:');
console.log(`- 地面偏移量 = ${scaledGroundOffset}`);
console.log(`- 模型Y坐标 = 0 + ${scaledGroundOffset} = ${scaledGroundOffset}`);
console.log(`- 模型实际底部在 Y = ${scaledGroundOffset} + ${scaledBoundingBox.min.y} = ${scaledGroundOffset + scaledBoundingBox.min.y}`);
console.log(`- 结果: 模型正确贴紧地面!`);

console.log('\n=== 修复方案 ===');
console.log('1. 先应用缩放和旋转: applyScaleAndRotation()');
console.log('2. 然后计算边界框: calculateBoundingBox()');
console.log('3. 最后应用位置: applyPosition()');
console.log('');
console.log('这样确保地面偏移量是基于缩放后的实际模型尺寸计算的。');
