// 测试地面间隙功能
// 验证模型底部距离地面0.2个单位的效果

console.log('=== 地面间隙功能测试 ===');

const GROUND_CLEARANCE = 0.2;

// 测试用例
const testCases = [
  {
    name: '基础测试：Y=0的模型',
    config: { x: 0, y: 0, z: 0 },
    boundingBox: { min: { y: -6.2 }, max: { y: 3.0 } },
    description: '模型底部应该距离地面0.2个单位'
  },
  {
    name: '高度测试：Y=5的模型',
    config: { x: 0, y: 5, z: 0 },
    boundingBox: { min: { y: -6.2 }, max: { y: 3.0 } },
    description: '模型底部应该距离地面5.2个单位'
  },
  {
    name: '负偏移测试：底部在原点之上的模型',
    config: { x: 0, y: 0, z: 0 },
    boundingBox: { min: { y: 2.0 }, max: { y: 8.0 } },
    description: '模型底部应该距离地面0.2个单位'
  },
  {
    name: '零偏移测试：底部正好在原点的模型',
    config: { x: 0, y: 0, z: 0 },
    boundingBox: { min: { y: 0.0 }, max: { y: 5.0 } },
    description: '模型底部应该距离地面0.2个单位'
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
  console.log(`描述: ${testCase.description}`);
  console.log(`配置位置: Y = ${testCase.config.y}`);
  console.log(`边界框: min.y = ${testCase.boundingBox.min.y}, max.y = ${testCase.boundingBox.max.y}`);
  
  // 计算地面偏移量
  const groundOffset = -testCase.boundingBox.min.y;
  console.log(`地面偏移量: ${groundOffset}`);
  
  // 应用新的逻辑（包含0.2间隙）
  const finalY = testCase.config.y + groundOffset + GROUND_CLEARANCE;
  console.log(`最终Y坐标: ${testCase.config.y} + ${groundOffset} + ${GROUND_CLEARANCE} = ${finalY}`);
  
  // 计算模型底部的实际位置
  const modelBottomY = finalY + testCase.boundingBox.min.y;
  console.log(`模型底部实际Y坐标: ${finalY} + ${testCase.boundingBox.min.y} = ${modelBottomY}`);
  
  // 计算距离地面的高度
  const expectedGroundHeight = testCase.config.y + GROUND_CLEARANCE;
  console.log(`预期距离地面高度: ${testCase.config.y} + ${GROUND_CLEARANCE} = ${expectedGroundHeight}`);
  
  // 验证结果
  const isCorrect = Math.abs(modelBottomY - expectedGroundHeight) < 0.001;
  console.log(`验证结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
  
  if (isCorrect) {
    console.log(`🎯 模型底部正确距离地面 ${expectedGroundHeight} 个单位`);
  } else {
    console.log(`❌ 模型底部实际距离地面 ${modelBottomY} 个单位，预期 ${expectedGroundHeight} 个单位`);
  }
});

console.log('\n=== 总结 ===');
console.log('微调效果:');
console.log(`✅ 所有模型底部现在距离地面 ${GROUND_CLEARANCE} 个单位`);
console.log('✅ Y=0 的模型：底部距离地面 0.2 个单位');
console.log('✅ Y=5 的模型：底部距离地面 5.2 个单位');
console.log('✅ 保持了原有的相对高度关系，只是整体抬高了 0.2 个单位');

console.log('\n关键修改:');
console.log('// 添加地面间隙常量');
console.log('const GROUND_CLEARANCE = 0.2;');
console.log('');
console.log('// 在地面偏移量基础上加上间隙');
console.log('yPosition = yPosition + groundOffset.value + GROUND_CLEARANCE;');

console.log('\n配置说明:');
console.log('- 如果需要调整间隙大小，只需修改 GROUND_CLEARANCE 常量');
console.log('- 如果某个模型需要特殊的间隙，可以考虑在模型配置中添加 groundClearance 属性');
console.log('- 当前所有模型统一使用 0.2 个单位的间隙');
