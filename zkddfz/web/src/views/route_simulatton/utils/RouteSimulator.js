/**
 * 路线模拟器
 * 支持WebSocket实时数据和JSON数组模拟
 */

import * as THREE from 'three';
import { EasingFunctions } from './animationUtils.js';
import { SmoothAnimationController } from './SmoothAnimationController.js';

class RouteSimulator {
  constructor(options = {}) {
    this.model = null;
    this.modelComponent = null; // ModelComponent引用，用于正确的坐标转换
    this.isRunning = false;
    this.isPaused = false;
    this.currentIndex = 0;
    this.routeData = [];
    this.websocket = null;

    // 延时启动管理
    this.delayedStartTimers = new Map(); // device_id -> timer_id
    this.globalStartTime = null; // 全局开始时间基准
    this.deviceStartTimes = new Map(); // device_id -> start_time

    // 配置选项
    this.options = {
      interpolationMode: 'linear', // linear, smooth, bezier
      speed: 1.0, // 仿真速度倍率
      loop: false, // 是否循环仿真
      autoStart: false, // 是否自动开始
      smoothRotation: true, // 是否平滑旋转
      enableSmoothAnimation: true, // 启用流畅动画控制器
      debugMode: false, // 调试模式，控制日志输出
      ...options
    };

    // 动画相关
    this.animationId = null;
    this.startTime = 0;
    this.currentAnimation = null;

    // 流畅动画控制器
    this.smoothAnimationController = new SmoothAnimationController({
      bufferSize: 3,
      predictionTime: 1.5,
      smoothingFactor: 0.8,
      maxInterpolationTime: 2.0,
      adaptiveBuffer: true
    });

    // 回调函数
    this.onPositionUpdate = null;
    this.onRouteComplete = null;
    this.onError = null;

    // 设置流畅动画控制器的回调
    this.smoothAnimationController.setPositionUpdateCallback((data) => {
      this.handleSmoothPositionUpdate(data);
    });

    console.log('RouteSimulator initialized with options:', this.options);
  }
  
  /**
   * 设置要控制的模型
   */
  setModel(model) {
    this.model = model;
    console.log('Model set for route simulation');
  }

  /**
   * 设置ModelComponent引用（用于正确的坐标转换）
   */
  setModelComponent(modelComponent) {
    this.modelComponent = modelComponent;
    console.log('ModelComponent set for route simulation');
  }

  /**
   * 设置所有模型的引用（用于WebSocket车辆控制）
   */
  setAllModels(models) {
    this.allModels = models;
    console.log('All models set for WebSocket vehicle control');
  }

  /**
   * 设置场景管理器引用（用于车辆控制）
   */
  setSceneManager(sceneManager) {
    this.sceneManager = sceneManager;
    console.log('Scene manager set for vehicle control');
  }
  
  /**
   * 加载路线数据（JSON数组）
   */
  loadRouteData(data) {
    try {
      this.routeData = this.validateRouteData(data);
      this.currentIndex = 0;
      console.log(`Route data loaded: ${this.routeData.length} waypoints`);
      
      if (this.options.autoStart && this.routeData.length > 0) {
        this.start();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to load route data:', error);
      if (this.onError) this.onError(error);
      return false;
    }
  }
  
  /**
   * 验证路线数据格式
   */
  validateRouteData(data) {
    if (!Array.isArray(data)) {
      throw new Error('Route data must be an array');
    }

    return data.map((point, index) => {
      const validated = {
        timestamp: point.timestamp || Date.now() + index * 1000,
        position: {
          x: parseFloat(point.position?.x || point.x || 0),
          y: parseFloat(point.position?.y || point.y || 0),
          z: parseFloat(point.position?.z || point.z || 0)
        },
        rotation: {
          x: parseFloat(point.rotation?.x || point.rotX || 0),
          y: parseFloat(point.rotation?.y || point.rotY || point.yaw || 0),
          z: parseFloat(point.rotation?.z || point.rotZ || 0)
        },
        speed: parseFloat(point.speed || 1.0),
        duration: parseFloat(point.duration || 1000), // 到达此点的持续时间(ms)

        // 新的电文结构支持
        meshType: point.meshType || (point.meshName === 'body' ? 'body' : 'component'),
        componentTurn: this.validateComponentTurn(point.componentTurn || []),

        // 保持向后兼容
        meshName: point.meshName || 'body'
      };

      return validated;
    });
  }

  /**
   * 验证componentTurn数组格式
   */
  validateComponentTurn(componentTurn) {
    if (!Array.isArray(componentTurn)) {
      return [];
    }

    return componentTurn.map(component => ({
      duration: parseFloat(component.duration || 1000),
      rotation: {
        x: parseFloat(component.rotation?.x || 0),
        y: parseFloat(component.rotation?.y || 0),
        z: parseFloat(component.rotation?.z || 0)
      },
      meshName: component.meshName || 'unknown'
    }));
  }
  
  /**
   * 连接WebSocket进行实时数据接收
   */
  connectWebSocket(url, protocols = []) {
    try {
      // 关闭现有连接
      if (this.websocket) {
        this.websocket.close();
      }

      this.websocket = new WebSocket(url, protocols);

      this.websocket.onopen = () => {
        console.log('WebSocket 连接已建立:', url);

        // 发送连接确认消息
        this.sendWebSocketMessage({
          type: 'connection',
          action: 'hello',
          data: {
            clientType: 'route_simulator',
            timestamp: Date.now()
          }
        });
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('收到 WebSocket 消息:', message);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('WebSocket 消息解析失败:', error);
          if (this.onError) this.onError(error);
        }
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket 连接错误:', error);
        if (this.onError) this.onError(error);
      };

      this.websocket.onclose = (event) => {
        console.log('WebSocket 连接已关闭:', event.code, event.reason);
        this.websocket = null;
      };

      return true;
    } catch (error) {
      console.error('WebSocket 连接失败:', error);
      if (this.onError) this.onError(error);
      return false;
    }
  }
  
  /**
   * 处理 WebSocket 消息
   */
  handleWebSocketMessage(message) {
    // 检查是否是车辆控制电文数组格式
    if (Array.isArray(message)) {
      this.handleVehicleControlMessage(message);
      return;
    }

    const { type, action, data } = message;

    switch (type) {
      case 'connection':
        this.handleConnectionMessage(action, data);
        break;
      case 'route_data':
        this.handleRouteDataMessage(action, data);
        break;
      case 'status':
        this.handleStatusMessage(action, data);
        break;
      case 'error':
        this.handleErrorMessage(action, data);
        break;
      default:
        console.warn('未知的 WebSocket 消息类型:', type);
    }
  }

  /**
   * 处理连接消息
   */
  handleConnectionMessage(action, data) {
    switch (action) {
      case 'connected':
        console.log('WebSocket 服务器确认连接:', data);
        break;
      default:
        console.log('连接消息:', action, data);
    }
  }

  /**
   * 处理路线数据消息
   */
  handleRouteDataMessage(action, data) {
    if (!this.model) {
      console.warn('模型未设置，无法处理路线数据');
      return;
    }

    switch (action) {
      case 'point':
        // 单个路线点的实时数据
        this.handleRealtimePoint(data);
        break;
      case 'batch':
        // 批量路线数据
        this.handleBatchRouteData(data);
        break;
      default:
        console.warn('未知的路线数据动作:', action);
    }
  }

  /**
   * 处理状态消息
   */
  handleStatusMessage(action, data) {
    console.log('状态消息:', action, data);
  }

  /**
   * 处理错误消息
   */
  handleErrorMessage(action, data) {
    console.error('服务器错误:', action, data);
    if (this.onError) this.onError(new Error(data));
  }

  /**
   * 处理流畅动画位置更新
   */
  handleSmoothPositionUpdate(data) {
    const { deviceId, position, rotation, velocity, smooth, timestamp, directUpdate, progress } = data;

    // 调试日志
    if (Math.random() < 0.01) { // 1%的概率输出日志
      console.log(`RouteSimulator 接收回调: ${deviceId}, 位置=(${position.x.toFixed(1)}, ${position.z.toFixed(1)}), directUpdate=${directUpdate}`);
    }

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn(`allModels 未初始化或不是数组`);
      return;
    }

    const targetModel = this.allModels.find(model => model.id === deviceId);
    if (!targetModel) {
      console.warn(`未找到设备 ${deviceId} 的模型`);
      return;
    }

    // 更新模型数据
    targetModel.currentPosition = position;
    targetModel.currentRotation = rotation;

    // 如果是直接更新模式，直接操作3D模型，跳过ModelComponent的动画
    if (directUpdate && this.sceneManager) {
      this.directUpdateModel3D(deviceId, position, rotation);
    } else {
      // 使用原有的更新方式
      if (this.sceneManager && this.sceneManager.updateModelPosition) {
        this.sceneManager.updateModelPosition(deviceId, position, rotation);
      }
    }

    // 触发位置更新回调（如果存在）
    if (this.onPositionUpdate) {
      this.onPositionUpdate({
        modelId: deviceId,
        device_id: deviceId,
        position: position,
        rotation: rotation,
        velocity: velocity,
        smooth: smooth,
        progress: progress,
        timestamp: timestamp
      });
    }
  }

  /**
   * 处理车辆控制电文
   */
  handleVehicleControlMessage(vehicleCommands) {
    console.log('接收到车辆控制电文:', vehicleCommands);

    if (!Array.isArray(vehicleCommands)) {
      console.warn('车辆控制电文格式错误，期望数组');
      return;
    }

    // 如果接收到空数组，停止所有流畅动画和延时启动
    if (vehicleCommands.length === 0) {
      console.log('接收到空数组，停止所有车辆动画和延时启动');
      this.clearAllDelayedStarts();
      if (this.options.enableSmoothAnimation && this.smoothAnimationController) {
        this.smoothAnimationController.stopAllAnimations();
        console.log('所有流畅动画已停止');
      }
      return;
    }

    // 设置全局开始时间基准（如果还没有设置）
    if (this.globalStartTime === null) {
      this.globalStartTime = Date.now();
      console.log(`🕐 设置全局开始时间基准: ${new Date(this.globalStartTime).toLocaleTimeString()}`);
    }

    // 处理延时启动
    this.processDelayedStartCommands(vehicleCommands);
  }

  /**
   * 处理延时启动命令
   */
  processDelayedStartCommands(vehicleCommands) {
    // 按设备ID分组命令
    const commandsByDevice = new Map();

    vehicleCommands.forEach(command => {
      const { device_id, start_time } = command;

      if (!commandsByDevice.has(device_id)) {
        commandsByDevice.set(device_id, []);
      }
      commandsByDevice.get(device_id).push(command);

      // 记录设备的开始时间
      if (start_time !== undefined) {
        this.deviceStartTimes.set(device_id, start_time);
      }
    });

    // 为每个设备设置延时启动
    commandsByDevice.forEach((commands, device_id) => {
      this.scheduleDeviceStart(device_id, commands);
    });
  }

  /**
   * 调度设备延时启动
   */
  scheduleDeviceStart(device_id, commands) {
    // 清除该设备之前的延时启动定时器
    this.clearDelayedStart(device_id);

    // 获取设备的开始时间
    const deviceStartTime = this.deviceStartTimes.get(device_id) || 0;

    // 计算延时时间（相对于全局开始时间）
    const currentTime = Date.now();
    const targetStartTime = this.globalStartTime + deviceStartTime;
    const delayMs = Math.max(0, targetStartTime - currentTime);

    console.log(`⏰ 设备 ${device_id} 延时启动设置:`);
    console.log(`  - 设备开始时间: ${deviceStartTime}ms`);
    console.log(`  - 目标启动时间: ${new Date(targetStartTime).toLocaleTimeString()}`);
    console.log(`  - 延时时间: ${delayMs}ms`);

    // 设置延时启动定时器
    const timerId = setTimeout(() => {
      console.log(`🚀 设备 ${device_id} 开始执行 (延时 ${delayMs}ms 后)`);
      this.executeDeviceCommands(device_id, commands);
      this.delayedStartTimers.delete(device_id);
    }, delayMs);

    // 保存定时器ID
    this.delayedStartTimers.set(device_id, timerId);
  }

  /**
   * 执行设备命令
   */
  executeDeviceCommands(device_id, commands) {
    console.log(`📋 执行设备 ${device_id} 的 ${commands.length} 个命令`);

    commands.forEach((command, index) => {
      console.log(`  命令 ${index + 1}/${commands.length}:`, command);

      if (this.options.enableSmoothAnimation) {
        this.processSmoothVehicleCommand(command);
      } else {
        this.executeVehicleCommand(command);
      }
    });
  }

  /**
   * 处理流畅车辆命令（使用流畅动画控制器）
   */
  processSmoothVehicleCommand(command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`处理流畅车辆命令: ${device_id}`, command);

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn('车辆模型列表未设置，无法处理命令');
      return;
    }

    const targetModel = this.allModels.find(model => model.id === device_id);
    if (!targetModel) {
      console.warn(`⚠️ 未找到设备ID为 ${device_id} 的车辆模型`);
      return;
    }

    // 立即更新电量、消耗和距离（不需要动画）
    if (typeof power === 'number') {
      targetModel.battery = power;
    }

    if (typeof consume === 'number') {
      targetModel.consume = consume;
    }

    if (typeof distance === 'number') {
      targetModel.distance = distance;
    }

    // 如果有位置或旋转数据，添加到流畅动画控制器
    if (position || rotation) {
      // 验证和修正位置数据
      const validatedPosition = this.validateAndFixPosition(
        position || targetModel.currentPosition || { x: 0, y: 0, z: 0 }
      );

      const validatedRotation = this.validateAndFixRotation(
        rotation || targetModel.currentRotation || { x: 0, y: 0, z: 0 }
      );

      const dataPoint = {
        position: validatedPosition,
        rotation: validatedRotation,
        power: power,
        consume: consume,
        distance: distance,
        duration: duration,
        timestamp: start_time || Date.now()
      };

      console.log(`设备 ${device_id} 验证后的数据点:`, {
        original: { position, rotation },
        validated: { position: validatedPosition, rotation: validatedRotation }
      });

      this.smoothAnimationController.addDataPoint(device_id, dataPoint);
    }
  }

  /**
   * 执行单个车辆控制命令（原有方法）
   */
  executeVehicleCommand(command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`执行车辆控制命令: ${device_id}`, command);

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn('车辆模型列表未设置，无法执行控制命令');
      return;
    }

    const targetModel = this.allModels.find(model => model.id === device_id);
    if (!targetModel) {
      console.warn(`⚠️ 未找到设备ID为 ${device_id} 的车辆模型`);
      return;
    }

    // 如果有duration且大于0，执行顺滑动画
    if (duration && duration > 0 && (position || rotation)) {
      this.executeVehicleAnimationCommand(targetModel, command);
    } else {
      // 立即更新位置和角度（无动画）
      this.executeVehicleImmediateCommand(targetModel, command);
    }

    // 立即更新电量、消耗和距离（不需要动画）
    if (typeof power === 'number') {
      targetModel.battery = power;
    }

    if (typeof consume === 'number') {
      targetModel.consume = consume;
    }

    if (typeof distance === 'number') {
      targetModel.distance = distance;
    }
  }

  /**
   * 执行车辆动画命令（基于duration的顺滑动画）
   */
  executeVehicleAnimationCommand(targetModel, command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`执行车辆动画命令: ${device_id}, 持续时间: ${duration}ms`);

    // 获取当前位置和角度
    const currentPosition = {
      x: targetModel.currentPosition?.x || 0,
      y: targetModel.currentPosition?.y || 0,
      z: targetModel.currentPosition?.z || 0
    };

    const currentRotation = {
      x: targetModel.currentRotation?.x || 0,
      y: targetModel.currentRotation?.y || 0,
      z: targetModel.currentRotation?.z || 0
    };

    // 目标位置和角度
    const targetPosition = position ? { ...currentPosition, ...position } : currentPosition;
    const targetRotation = rotation ? { ...currentRotation, ...rotation } : currentRotation;

    // 创建动画
    this.createVehicleAnimation(targetModel, {
      startPosition: currentPosition,
      targetPosition: targetPosition,
      startRotation: currentRotation,
      targetRotation: targetRotation,
      duration: duration,
      startPower: targetModel.battery || 0,
      targetPower: typeof power === 'number' ? power : targetModel.battery || 0,
      startConsume: targetModel.consume || 0,
      targetConsume: typeof consume === 'number' ? consume : targetModel.consume || 0,
      startDistance: targetModel.distance || 0,
      targetDistance: typeof distance === 'number' ? distance : targetModel.distance || 0
    });
  }

  /**
   * 执行车辆立即命令（无动画）
   */
  executeVehicleImmediateCommand(targetModel, command) {
    const { position, rotation } = command;

    // 立即更新车辆的目标位置和角度
    if (position) {
      // 确保currentPosition存在
      if (!targetModel.currentPosition) {
        targetModel.currentPosition = {};
      }
      Object.assign(targetModel.currentPosition, position);
    }

    if (rotation) {
      // 确保currentRotation存在
      if (!targetModel.currentRotation) {
        targetModel.currentRotation = {};
      }
      Object.assign(targetModel.currentRotation, rotation);
    }
  }

  /**
   * 创建车辆动画（基于WebSocket duration）
   */
  createVehicleAnimation(targetModel, animationConfig) {
    const {
      startPosition,
      targetPosition,
      startRotation,
      targetRotation,
      duration,
      startPower,
      targetPower,
      startConsume,
      targetConsume,
      startDistance,
      targetDistance
    } = animationConfig;

    // 如果已有动画在运行，先停止
    if (targetModel.currentAnimation) {
      targetModel.currentAnimation.stop();
    }

    const startTime = performance.now();
    let animationId = null;

    console.log(`开始车辆匀速动画: ${targetModel.id}`, {
      from: startPosition,
      to: targetPosition,
      duration: `${duration}ms`,
      type: 'linear'
    });

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用匀速线性插值（确保动画衔接）
      const easedProgress = progress;

      // 插值计算当前位置
      const currentPos = {
        x: this.lerp(startPosition.x, targetPosition.x, easedProgress),
        y: this.lerp(startPosition.y, targetPosition.y, easedProgress),
        z: this.lerp(startPosition.z, targetPosition.z, easedProgress)
      };

      // 插值计算当前角度
      const currentRot = {
        x: this.lerp(startRotation.x, targetRotation.x, easedProgress),
        y: this.lerpAngle(startRotation.y, targetRotation.y, easedProgress),
        z: this.lerp(startRotation.z, targetRotation.z, easedProgress)
      };

      // 插值计算当前电量、消耗和距离
      const currentPower = Math.round(this.lerp(startPower, targetPower, easedProgress));
      const currentConsume = Math.round(this.lerp(startConsume, targetConsume, easedProgress));
      const currentDistance = Math.round(this.lerp(startDistance, targetDistance, easedProgress));

      // 更新模型数据
      targetModel.currentPosition = currentPos;
      targetModel.currentRotation = currentRot;
      targetModel.battery = currentPower;
      targetModel.consume = currentConsume;
      targetModel.distance = currentDistance;

      // 如果有场景管理器，通知更新模型位置
      if (this.sceneManager && this.sceneManager.updateModelPosition) {
        this.sceneManager.updateModelPosition(targetModel.id, currentPos, currentRot);
      }

      // 触发位置更新回调（如果存在）
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          modelId: targetModel.id,
          device_id: targetModel.id,
          position: currentPos,
          rotation: currentRot,
          battery: currentPower,
          consume: currentConsume,
          distance: currentDistance,
          progress: progress
        });
      }

      // 继续动画或完成
      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      } else {
        // 动画完成
        console.log(`车辆动画完成: ${targetModel.id}`);
        targetModel.currentAnimation = null;

        // 确保最终值精确
        targetModel.currentPosition = targetPosition;
        targetModel.currentRotation = targetRotation;
        targetModel.battery = targetPower;
        targetModel.consume = targetConsume;
        targetModel.distance = targetDistance;
      }
    };

    // 开始动画
    animationId = requestAnimationFrame(animate);

    // 保存动画控制对象
    targetModel.currentAnimation = {
      stop: () => {
        if (animationId) {
          cancelAnimationFrame(animationId);
          animationId = null;
        }
        targetModel.currentAnimation = null;
        console.log(`车辆动画已停止: ${targetModel.id}`);
      }
    };
  }

  /**
   * 线性插值函数
   */
  lerp(start, end, t) {
    return start + (end - start) * t;
  }

  /**
   * 角度插值函数（处理角度环绕）
   */
  lerpAngle(start, end, t) {
    let diff = end - start;

    // 处理角度环绕（选择最短路径）
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }

    return start + diff * t;
  }

  /**
   * 平滑缓动函数（已停用，改用匀速线性插值确保动画衔接）
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * 线性插值函数（匀速动画，确保每段动画能够完美衔接）
   */
  linear(t) {
    return t;
  }

  /**
   * 处理实时路线点
   */
  handleRealtimePoint(data) {
    try {
      const point = this.validateRouteData([data])[0];
      this.moveToPoint(point, true); // 实时移动，不使用动画

      console.log('实时移动到位置:', point.position);
    } catch (error) {
      console.error('处理实时路线点失败:', error);
    }
  }

  /**
   * 处理批量路线数据
   */
  handleBatchRouteData(data) {
    try {
      if (Array.isArray(data)) {
        this.loadRouteData(data);
        console.log('批量路线数据已加载:', data.length, '个点');
      } else {
        console.warn('批量路线数据格式错误，期望数组');
      }
    } catch (error) {
      console.error('处理批量路线数据失败:', error);
    }
  }

  /**
   * 发送 WebSocket 消息
   */
  sendWebSocketMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      try {
        const json = JSON.stringify(message);
        this.websocket.send(json);
        console.log('发送 WebSocket 消息:', message);
      } catch (error) {
        console.error('发送 WebSocket 消息失败:', error);
      }
    } else {
      console.warn('WebSocket 未连接，无法发送消息');
    }
  }
  
  /**
   * 开始路线模拟
   */
  start() {
    console.log('RouteSimulator.start() called');
    console.log('Model available:', !!this.model);
    console.log('Route data length:', this.routeData.length);

    if (!this.model) {
      console.warn('Cannot start: model not available');
      console.log('Model object:', this.model);
      return false;
    }

    if (this.routeData.length === 0) {
      console.warn('Cannot start: route data not available');
      console.log('Route data:', this.routeData);
      return false;
    }

    this.isRunning = true;
    this.isPaused = false;
    this.startTime = performance.now();

    console.log('Route simulation started with', this.routeData.length, 'waypoints');
    this.playRoute();
    return true;
  }
  
  /**
   * 暂停路线模拟
   */
  pause() {
    this.isPaused = true;
    if (this.currentAnimation) {
      this.currentAnimation.pause();
    }
    console.log('Route simulation paused');
  }
  
  /**
   * 恢复路线模拟
   */
  resume() {
    this.isPaused = false;
    if (this.currentAnimation) {
      this.currentAnimation.resume();
    } else {
      this.playRoute();
    }
    console.log('Route simulation resumed');
  }
  
  /**
   * 停止路线模拟
   */
  stop() {
    this.isRunning = false;
    this.isPaused = false;
    this.currentIndex = 0;
    
    if (this.currentAnimation) {
      this.currentAnimation.stop();
      this.currentAnimation = null;
    }
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    console.log('Route simulation stopped');
  }
  
  /**
   * 仿真路线
   */
  playRoute() {
    if (!this.isRunning || this.isPaused || this.currentIndex >= this.routeData.length) {
      if (this.currentIndex >= this.routeData.length) {
        this.handleRouteComplete();
      }
      return;
    }
    
    const currentPoint = this.routeData[this.currentIndex];
    const nextPoint = this.routeData[this.currentIndex + 1];
    
    if (nextPoint) {
      this.moveToPoint(nextPoint, false, () => {
        this.currentIndex++;
        this.playRoute(); // 继续下一个点
      });
    } else {
      this.handleRouteComplete();
    }
  }
  
  /**
   * 移动到指定点
   */
  moveToPoint(point, immediate = false, onComplete = null) {
    if (!this.model) return;
    console.log("开始拐弯")
    // 特殊处理：当point.position.x == 47.927308061452，移动障碍物模型
    if (point.position.x === 47.927308061452) {
      this.moveObstacleOnSpecialCondition();
    }

    // 使用地面坐标系的位置数据
    const groundPosition = {
      x: point.position.x+675-100,
      y: 0.2,
      z: point.position.z-496-55.5
    };

    const targetRotation = new THREE.Euler(
      THREE.MathUtils.degToRad(point.rotation.x),
      THREE.MathUtils.degToRad(-(point.rotation.y+90)),
      THREE.MathUtils.degToRad(point.rotation.z)
    );

    // 检查电文类型，决定旋转方式
    const meshType = point.meshType || "body";
    const isBodyRotation = meshType === "body";

    if (immediate) {
      // 立即移动（用于实时数据）
      if (this.modelComponent) {
        // 使用ModelComponent的方法，确保正确的坐标转换和配置更新
        this.modelComponent.updateCurrentPosition(groundPosition);

        // 根据meshType决定旋转方式
        if (isBodyRotation) {
          // 整体模型旋转
          const rotationDegrees = {
            x: point.rotation.x,
            y: point.rotation.y,
            z: point.rotation.z
          };
          this.modelComponent.updateCurrentRotation(rotationDegrees);
        } else {
          // 组件旋转模式：同时旋转多个mesh
          this.rotateMultipleMeshes(point.componentTurn);
        }
      } else {
        // 备用方法：直接设置模型位置和旋转（可能不准确）
        console.warn('ModelComponent not available, using direct position setting');
        const worldPos = this.convertToWorldPosition(groundPosition);
        this.model.position.copy(worldPos);

        if (isBodyRotation) {
          this.model.rotation.copy(targetRotation);
        } else {
          // 组件旋转模式：同时旋转多个mesh
          this.rotateMultipleMeshes(point.componentTurn);
        }
      }

      if (this.onPositionUpdate) {
        this.onPositionUpdate(point);
      }
    } else {
      // 动画移动（用于路线仿真）
      const duration = point.duration / this.options.speed;

      // 验证和修正旋转数据
      const correctedData = this.validateAndCorrectRotationData(point.rotation, duration);

      console.log(`📦 电文数据验证:`);
      console.log(`- 原始duration: ${point.duration}ms, 修正后: ${correctedData.duration}ms`);
      console.log(`- 原始旋转: y=${point.rotation.y}°, 修正后: y=${correctedData.rotation.y}°`);

      this.animateToPoint(groundPosition, targetRotation, correctedData.duration, onComplete, point.meshType, correctedData.rotation, point.componentTurn);
    }
  }

  /**
   * 转换地面坐标到世界坐标（备用方法）
   */
  convertToWorldPosition(groundPos) {
    // 这是一个简化的转换，实际应该使用ModelComponent的方法
    const groundOffset = this.modelComponent?.getGroundOffset?.() || 0;
    return new THREE.Vector3(
      groundPos.x,
      groundPos.y + groundOffset,
      groundPos.z
    );
  }
  
  /**
   * 动画移动到指定点
   */
  animateToPoint(groundPosition, targetRotation, duration, onComplete, meshType, originalRotation = null, componentTurn = []) {
    if (!this.model) return;

    const isBodyRotation = meshType === "body";

    // 转换旋转为度数
    const targetRotationDegrees = {
      x: THREE.MathUtils.radToDeg(targetRotation.x),
      y: THREE.MathUtils.radToDeg(targetRotation.y),
      z: THREE.MathUtils.radToDeg(targetRotation.z)
    };

    // 使用ModelComponent的moveToPositionAndRotation方法进行平滑移动和旋转（仅当整体旋转时）
    if (this.modelComponent && this.modelComponent.moveToPositionAndRotation && isBodyRotation) {
      console.log('Using ModelComponent.moveToPositionAndRotation for smooth animation');

      // 计算移动速度（单位/秒）
      const currentPos = this.modelComponent.getCurrentPosition();
      const distance = Math.sqrt(
        Math.pow(groundPosition.x - currentPos.x, 2) +
        Math.pow(groundPosition.y - currentPos.y, 2) +
        Math.pow(groundPosition.z - currentPos.z, 2)
      );
      const speed = distance / (duration / 1000); // 转换为单位/秒

      // 根据插值模式选择缓动类型
      const easingType = this.options.interpolationMode === 'smooth' ? 'easeInOutQuad' : 'linear';

      // 使用ModelComponent的同时移动和旋转方法
      this.modelComponent.moveToPositionAndRotation(groundPosition, targetRotationDegrees, speed, easingType)
        .then(() => {
          if (onComplete) onComplete();
        })
        .catch((error) => {
          console.error('Animation failed:', error);
          if (onComplete) onComplete();
        });
    } else if (this.modelComponent && this.modelComponent.moveToPosition) {
      console.log('Using ModelComponent.moveToPosition for animation (fallback)');

      // 计算移动速度（单位/秒）
      const currentPos = this.modelComponent.getCurrentPosition();
      const distance = Math.sqrt(
        Math.pow(groundPosition.x - currentPos.x, 2) +
        Math.pow(groundPosition.y - currentPos.y, 2) +
        Math.pow(groundPosition.z - currentPos.z, 2)
      );
      const speed = distance / (duration / 1000); // 转换为单位/秒

      // 使用ModelComponent的移动方法
      this.modelComponent.moveToPosition(groundPosition, speed, 'linear', false)
        .then(() => {
          // 根据meshType决定旋转方式
          if (isBodyRotation) {
            // 整体模型旋转
            if (this.options.smoothRotation) {
              this.animateRotationOnly(targetRotation, duration, onComplete);
            } else {
              this.model.rotation.copy(targetRotation);
              if (onComplete) onComplete();
            }
          } else {
            // 组件旋转模式：同时旋转多个mesh
            this.animateMultipleMeshes(componentTurn, onComplete);
          }
        })
        .catch((error) => {
          console.error('Animation failed:', error);
          if (onComplete) onComplete();
        });
    } else {
      // 备用方法：直接动画
      console.warn('ModelComponent not available, using direct animation');
      this.animateDirectly(groundPosition, targetRotation, duration, onComplete, meshType, originalRotation, componentTurn);
    }
  }

  /**
   * 直接动画移动（备用方法）
   */
  animateDirectly(groundPosition, targetRotation, duration, onComplete, meshType = "body", originalRotation = null, componentTurn = []) {
    const worldTargetPosition = this.convertToWorldPosition(groundPosition);
    const startPosition = this.model.position.clone();
    const startRotation = this.model.rotation.clone();
    const startTime = performance.now();
    const isBodyRotation = meshType === "body";

    // 计算最短旋转路径（处理角度跨越边界的情况）
    const optimizedTargetRotation = this.optimizeRotationPath(startRotation, targetRotation);

    const animate = (currentTime) => {
      if (!this.isRunning || this.isPaused) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 选择缓动函数 - 为旋转使用更平滑的缓动
      const positionEasing = this.options.interpolationMode === 'smooth'
        ? EasingFunctions.easeInOutCubic(progress)
        : progress;

      // 旋转使用专门的平滑缓动
      const rotationEasing = this.options.smoothRotation
        ? EasingFunctions.easeInOutQuad(progress)  // 更平滑的旋转缓动
        : progress;

      // 位置插值
      this.model.position.lerpVectors(startPosition, worldTargetPosition, positionEasing);

      // 根据meshType决定旋转方式
      if (isBodyRotation) {
        // 整体模型旋转
        if (this.options.smoothRotation) {
          // 使用优化后的旋转路径进行插值
          this.model.rotation.x = this.smoothLerp(startRotation.x, optimizedTargetRotation.x, rotationEasing);
          this.model.rotation.y = this.smoothLerp(startRotation.y, optimizedTargetRotation.y, rotationEasing);
          this.model.rotation.z = this.smoothLerp(startRotation.z, optimizedTargetRotation.z, rotationEasing);
        } else {
          this.model.rotation.copy(targetRotation);
        }
      }

      // 触发位置更新回调
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          position: this.model.position,
          rotation: this.model.rotation,
          progress: progress
        });
      }

      if (progress < 1) {
        this.animationId = requestAnimationFrame(animate);
      } else {
        // 动画完成后处理组件旋转
        if (!isBodyRotation) {
          this.animateMultipleMeshes(componentTurn, onComplete);
        } else {
          if (onComplete) onComplete();
        }
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * 优化旋转路径，选择最短的旋转方向
   */
  optimizeRotationPath(startRotation, targetRotation) {
    const optimized = targetRotation.clone();

    // 对每个轴进行最短路径优化
    ['x', 'y', 'z'].forEach(axis => {
      const start = startRotation[axis];
      const target = targetRotation[axis];
      const diff = target - start;

      // 如果角度差超过π，选择反方向旋转
      if (Math.abs(diff) > Math.PI) {
        if (diff > 0) {
          optimized[axis] = target - 2 * Math.PI;
        } else {
          optimized[axis] = target + 2 * Math.PI;
        }
      }
    });

    return optimized;
  }

  /**
   * 平滑插值函数，带有额外的平滑处理
   */
  smoothLerp(start, end, t) {
    // 使用更平滑的插值算法
    const smoothT = t * t * (3 - 2 * t); // Smoothstep函数
    return start + (end - start) * smoothT;
  }

  /**
   * 仅旋转动画（用于位置移动完成后的旋转）
   */
  animateRotationOnly(targetRotation, duration, onComplete) {
    if (!this.model) return;

    const startRotation = this.model.rotation.clone();
    const optimizedTargetRotation = this.optimizeRotationPath(startRotation, targetRotation);
    const startTime = performance.now();

    const animate = (currentTime) => {
      if (!this.isRunning || this.isPaused) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用平滑的旋转缓动
      const rotationEasing = EasingFunctions.easeInOutQuad(progress);

      // 平滑旋转插值
      this.model.rotation.x = this.smoothLerp(startRotation.x, optimizedTargetRotation.x, rotationEasing);
      this.model.rotation.y = this.smoothLerp(startRotation.y, optimizedTargetRotation.y, rotationEasing);
      this.model.rotation.z = this.smoothLerp(startRotation.z, optimizedTargetRotation.z, rotationEasing);

      // 触发位置更新回调
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          position: this.model.position,
          rotation: this.model.rotation,
          progress: progress
        });
      }

      if (progress < 1) {
        this.animationId = requestAnimationFrame(animate);
      } else {
        if (onComplete) onComplete();
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }
  
  /**
   * 处理路线完成
   */
  handleRouteComplete() {
    console.log('Route simulation completed');
    
    if (this.options.loop && this.routeData.length > 0) {
      this.currentIndex = 0;
      this.playRoute();
    } else {
      this.isRunning = false;
      if (this.onRouteComplete) {
        this.onRouteComplete();
      }
    }
  }
  
  /**
   * 跳转到指定索引
   */
  jumpToIndex(index) {
    if (index >= 0 && index < this.routeData.length) {
      this.currentIndex = index;
      const point = this.routeData[index];
      this.moveToPoint(point, true);
    }
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentIndex: this.currentIndex,
      totalPoints: this.routeData.length,
      progress: this.routeData.length > 0 ? this.currentIndex / this.routeData.length : 0
    };
  }

  /**
   * 设置旋转平滑选项
   */
  setSmoothRotation(enabled) {
    this.options.smoothRotation = enabled;
    console.log('Smooth rotation:', enabled ? 'enabled' : 'disabled');
  }

  /**
   * 设置调试模式（控制日志输出以提高性能）
   */
  setDebugMode(enabled) {
    this.options.debugMode = enabled;
    console.log('Debug mode:', enabled ? 'enabled' : 'disabled');
  }

  /**
   * 批量清理mesh旋转缓存（性能优化）
   */
  clearMeshRotationCache() {
    if (!this.model) return;

    let clearedCount = 0;
    let clearedCollectionCount = 0;

    this.model.traverse((child) => {
      if (child.userData) {
        // 清理旋转缓存数据
        if (child.userData.rotationData) {
          child.userData.rotationData.cachedRelativePosition = null;
          child.userData.rotationData.rotationMatrix = null;
          clearedCount++;
        }

        // 清理子模型集合中心点缓存
        if (child.userData.cachedCollectionData) {
          child.userData.cachedCollectionData = null;
          clearedCollectionCount++;
        }

        // 清理其他临时缓存
        if (child.userData.cachedChildMeshes) {
          child.userData.cachedChildMeshes = null;
        }

        if (child.userData.tempMatrix) {
          child.userData.tempMatrix = null;
          child.userData.tempQuaternion = null;
        }
      }
    });

    if (this.options.debugMode) {
      console.log(`清理缓存完成: ${clearedCount}个旋转缓存, ${clearedCollectionCount}个集合中心点缓存`);
    }
  }

  /**
   * 获取旋转性能统计信息
   */
  getRotationPerformanceStats() {
    if (!this.model) return {
      totalMeshes: 0,
      cachedMeshes: 0,
      cachedCollections: 0,
      totalCollections: 0
    };

    let totalMeshes = 0;
    let cachedMeshes = 0;
    let totalCollections = 0;
    let cachedCollections = 0;

    this.model.traverse((child) => {
      if (child.userData) {
        // 统计旋转缓存
        if (child.userData.rotationData) {
          totalMeshes++;
          if (child.userData.rotationData.cachedRelativePosition) {
            cachedMeshes++;
          }
        }

        // 统计子模型集合缓存
        if (child.children && child.children.length > 0) {
          totalCollections++;
          if (child.userData.cachedCollectionData) {
            cachedCollections++;
          }
        }
      }
    });

    return {
      totalMeshes,
      cachedMeshes,
      totalCollections,
      cachedCollections,
      rotationCacheHitRate: totalMeshes > 0 ? (cachedMeshes / totalMeshes * 100).toFixed(1) + '%' : '0%',
      collectionCacheHitRate: totalCollections > 0 ? (cachedCollections / totalCollections * 100).toFixed(1) + '%' : '0%',
      overallCacheHitRate: (totalMeshes + totalCollections) > 0 ?
        ((cachedMeshes + cachedCollections) / (totalMeshes + totalCollections) * 100).toFixed(1) + '%' : '0%'
    };
  }

  /**
   * 设置插值模式
   */
  setInterpolationMode(mode) {
    this.options.interpolationMode = mode;
    console.log('Interpolation mode set to:', mode);
  }
  
  /**
   * 清除指定设备的延时启动
   */
  clearDelayedStart(device_id) {
    const timerId = this.delayedStartTimers.get(device_id);
    if (timerId) {
      clearTimeout(timerId);
      this.delayedStartTimers.delete(device_id);
      console.log(`🗑️ 清除设备 ${device_id} 的延时启动定时器`);
    }
  }

  /**
   * 清除所有延时启动
   */
  clearAllDelayedStarts() {
    console.log(`🗑️ 清除所有延时启动定时器 (共 ${this.delayedStartTimers.size} 个)`);

    this.delayedStartTimers.forEach((timerId, device_id) => {
      clearTimeout(timerId);
      console.log(`  - 清除设备 ${device_id} 的定时器`);
    });

    this.delayedStartTimers.clear();
    this.deviceStartTimes.clear();
    this.globalStartTime = null;
  }

  /**
   * 获取延时启动状态
   */
  getDelayedStartStatus() {
    const status = {
      globalStartTime: this.globalStartTime,
      activeTimers: this.delayedStartTimers.size,
      deviceStartTimes: Object.fromEntries(this.deviceStartTimes),
      pendingDevices: Array.from(this.delayedStartTimers.keys())
    };

    return status;
  }

  /**
   * 重置延时启动系统
   */
  resetDelayedStartSystem() {
    console.log('🔄 重置延时启动系统');
    this.clearAllDelayedStarts();
    console.log('✅ 延时启动系统已重置');
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    // 清除所有延时启动
    this.clearAllDelayedStarts();

    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }
  
  /**
   * 启用/禁用流畅动画
   */
  setSmoothAnimation(enabled) {
    this.options.enableSmoothAnimation = enabled;

    if (!enabled) {
      // 如果禁用流畅动画，停止所有流畅动画
      this.smoothAnimationController.stopAllAnimations();
    }

    console.log(`流畅动画已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取流畅动画状态
   */
  getSmoothAnimationStatus() {
    return {
      enabled: this.options.enableSmoothAnimation,
      performanceStats: this.smoothAnimationController.getPerformanceStats(),
      deviceStatuses: this.allModels ? this.allModels.map(model => ({
        deviceId: model.id,
        status: this.smoothAnimationController.getDeviceStatus(model.id)
      })) : []
    };
  }

  /**
   * 清理指定设备的流畅动画数据
   */
  clearSmoothAnimationData(deviceId) {
    this.smoothAnimationController.clearDevice(deviceId);
    console.log(`清理设备 ${deviceId} 的流畅动画数据`);
  }

  /**
   * 清理所有流畅动画数据
   */
  clearAllSmoothAnimationData() {
    this.smoothAnimationController.clearAll();
    console.log('清理所有流畅动画数据');
  }

  /**
   * 验证和修正位置数据
   */
  validateAndFixPosition(position) {
    const validated = {
      x: this.validateNumber(position.x, 0),
      y: this.validateNumber(position.y, 0), // 直接使用原始Y值，不进行任何修正
      z: this.validateNumber(position.z, 0)
    };

    return validated;
  }

  /**
   * 验证和修正旋转数据
   */
  validateAndFixRotation(rotation) {
    return {
      x: this.normalizeAngle(this.validateNumber(rotation.x, 0)),
      y: this.normalizeAngle(this.validateNumber(rotation.y, 0)),
      z: this.normalizeAngle(this.validateNumber(rotation.z, 0))
    };
  }

  /**
   * 验证数字有效性
   */
  validateNumber(value, defaultValue = 0) {
    if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
      console.warn(`⚠️ 无效数值 (${value})，使用默认值 ${defaultValue}`);
      return defaultValue;
    }
    return value;
  }

  /**
   * 标准化角度到 0 到 360 度范围
   */
  normalizeAngle(angle) {
    while (angle >= 360) angle -= 360;
    while (angle < 0) angle += 360;
    return angle;
  }

  /**
   * 计算最短旋转路径
   * @param {number} startAngle 起始角度（度）
   * @param {number} targetAngle 目标角度（度）
   * @returns {number} 旋转差值（度），正值为顺时针，负值为逆时针
   */
  calculateShortestRotationPath(startAngle, targetAngle) {
    // 确保角度在 0-360 范围内
    const normalizedStart = this.normalizeAngle(startAngle);
    const normalizedTarget = this.normalizeAngle(targetAngle);

    // 计算直接差值
    let diff = normalizedTarget - normalizedStart;

    // 选择最短路径
    if (diff > 180) {
      diff -= 360; // 逆时针更短
    } else if (diff < -180) {
      diff += 360; // 顺时针更短
    }

    return diff;
  }

  /**
   * 验证和修正电文中的旋转数据
   * @param {Object} rotationData 旋转数据 {x, y, z}
   * @param {number} duration 动画时长（毫秒）
   * @returns {Object} 修正后的旋转数据和时长
   */
  validateAndCorrectRotationData(rotationData, duration) {
    const corrected = {
      rotation: { ...rotationData },
      duration: duration
    };

    // 标准化角度值
    if (corrected.rotation.x !== undefined) {
      corrected.rotation.x = this.normalizeAngle(corrected.rotation.x);
    }
    if (corrected.rotation.y !== undefined) {
      corrected.rotation.y = this.normalizeAngle(corrected.rotation.y);
    }
    if (corrected.rotation.z !== undefined) {
      corrected.rotation.z = this.normalizeAngle(corrected.rotation.z);
    }

    // 确保duration不会太小
    const minDuration = 100; // 最小100ms
    if (corrected.duration < minDuration) {
      console.warn(`⚠️ 动画时长过短 (${duration}ms)，调整为最小值 ${minDuration}ms`);
      corrected.duration = minDuration;
    }

    // 检查是否有异常的角度值
    Object.keys(corrected.rotation).forEach(axis => {
      const originalValue = rotationData[axis];
      const correctedValue = corrected.rotation[axis];

      if (originalValue !== undefined && Math.abs(originalValue - correctedValue) > 1) {
        console.warn(`⚠️ 角度值 ${axis} 从 ${originalValue.toFixed(2)}° 标准化为 ${correctedValue.toFixed(2)}°`);
      }
    });

    return corrected;
  }

  /**
   * 测试空数组处理
   */
  testEmptyArrayHandling() {
    console.log('测试空数组处理...');

    // 先发送一些数据
    const testData = [
      {
        device_id: 'test_car',
        position: { x: 100, y: 0, z: 200 },
        rotation: { x: 0, y: 45, z: 0 },
        power: 80,
        duration: 1000
      }
    ];

    console.log('发送测试数据:', testData);
    this.handleVehicleControlMessage(testData);

    // 2秒后发送空数组
    setTimeout(() => {
      console.log('发送空数组');
      this.handleVehicleControlMessage([]);

      // 检查动画状态
      if (this.smoothAnimationController) {
        const status = this.getSmoothAnimationStatus();
        console.log('空数组处理后的状态:', status);
      }
    }, 2000);
  }

  /**
   * 测试Y坐标修正
   */
  testYCoordinateFix() {
    console.log('测试Y坐标修正...');

    const testData = [
      {
        device_id: 'test_car_y',
        position: { x: 100, y: -50, z: 200 }, // 异常的Y坐标
        rotation: { x: 0, y: 45, z: 0 },
        power: 80,
        duration: 1000
      }
    ];

    console.log('发送异常Y坐标数据:', testData);
    this.handleVehicleControlMessage(testData);
  }

  /**
   * 测试Y坐标显示问题
   */
  testYCoordinateShaking() {
    console.log('测试Y坐标显示问题修复...');

    // 发送一系列Y坐标都为0的数据，验证模型显示在正确的高度
    const testSequence = [
      { x: 100, y: 0, z: 200 },
      { x: 110, y: 0, z: 210 },
      { x: 120, y: 0, z: 220 },
      { x: 130, y: 0, z: 230 },
      { x: 140, y: 0, z: 240 }
    ];

    console.log('测试说明: Y坐标为0，模型应该显示在地面上，不会跳动或显示在地面以下');

    testSequence.forEach((position, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_y_display_car',
          position: position,
          rotation: { x: 0, y: 45, z: 0 },
          power: 80 - index * 5,
          duration: 1000
        }];

        console.log(`发送测试数据 ${index + 1}: Y坐标=${testData[0].position.y} (模型应该显示在地面上)`);
        this.handleVehicleControlMessage(testData);
      }, index * 1000);
    });

    console.log('Y坐标显示测试启动，观察模型是否正确显示在地面上');
  }

  /**
   * 测试不同Y坐标值的显示
   */
  testDifferentYCoordinates() {
    console.log('测试不同Y坐标值的显示...');

    const testSequence = [
      { x: 100, y: 0, z: 200, desc: '地面' },
      { x: 110, y: 5, z: 210, desc: '5米高' },
      { x: 120, y: 10, z: 220, desc: '10米高' },
      { x: 130, y: -5, z: 230, desc: '地下5米' },
      { x: 140, y: 0, z: 240, desc: '回到地面' }
    ];

    testSequence.forEach((data, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_y_values_car',
          position: { x: data.x, y: data.y, z: data.z },
          rotation: { x: 0, y: 45, z: 0 },
          power: 80 - index * 5,
          duration: 1000
        }];

        console.log(`发送测试数据 ${index + 1}: Y=${data.y} (${data.desc})`);
        this.handleVehicleControlMessage(testData);
      }, index * 1500);
    });

    console.log('不同Y坐标测试启动，观察模型高度变化是否正确');
  }

  /**
   * 测试延时队列效果
   */
  testUniformMotion() {
    console.log('测试延时队列动画效果...');

    // 确保启用延时队列模式
    if (this.smoothAnimationController) {
      this.smoothAnimationController.options.uniformMotion = true;
      console.log('已启用延时队列模式');
      console.log(`延时时间: ${this.smoothAnimationController.options.delayTime}ms`);
      console.log(`处理间隔: ${this.smoothAnimationController.options.processInterval}ms`);
    }

    // 发送一系列数据点，模拟车辆沿直线行驶
    const testSequence = [
      { x: 0, y: 0, z: 0 },
      { x: 20, y: 0, z: 0 },
      { x: 40, y: 0, z: 0 },
      { x: 60, y: 0, z: 0 },
      { x: 80, y: 0, z: 0 },
      { x: 100, y: 0, z: 0 }
    ];

    console.log('测试说明:');
    console.log('  1. 数据立即入队');
    console.log('  2. 第一个数据延时2秒后开始处理');
    console.log('  3. 后续数据每秒处理一个，实现流畅衔接');

    testSequence.forEach((position, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_queue_car',
          position: position,
          rotation: { x: 0, y: 90, z: 0 }, // 朝向X轴正方向
          power: 80 - index * 2,
          duration: 1000
        }];

        console.log(`[${new Date().toLocaleTimeString()}] 发送数据 ${index + 1}: 位置=(${position.x}, ${position.y}, ${position.z})`);
        this.handleVehicleControlMessage(testData);

        // 显示队列状态
        if (this.smoothAnimationController) {
          const status = this.smoothAnimationController.getDeviceStatus('test_queue_car');
          console.log(`队列状态: 长度=${status.queueSize}, 处理中=${status.isProcessing}`);
        }
      }, index * 500); // 每500ms发送一个数据点（比处理速度快）
    });

    console.log('延时队列测试启动，观察数据入队和处理过程');
  }

  /**
   * 测试数据流调试
   */
  testDataFlow() {
    console.log('测试数据流调试...');

    // 检查关键对象是否存在
    console.log('检查关键对象:');
    console.log('  - smoothAnimationController:', !!this.smoothAnimationController);
    console.log('  - sceneManager:', !!this.sceneManager);
    console.log('  - allModels:', !!this.allModels, '长度:', this.allModels ? this.allModels.length : 'N/A');

    // 发送一个简单的测试数据
    const testData = [{
      device_id: 'test_debug_car',
      position: { x: 50, y: 0, z: 50 },
      rotation: { x: 0, y: 90, z: 0 },
      power: 80,
      duration: 2000 // 2秒动画
    }];

    console.log('发送调试数据:', testData[0]);
    console.log('观察控制台日志，追踪数据流向:');
    console.log('  1. 数据入队');
    console.log('  2. 延时2秒后开始处理');
    console.log('  3. 动画更新');
    console.log('  4. 3D模型更新');

    this.handleVehicleControlMessage(testData);

    // 显示队列状态
    setTimeout(() => {
      if (this.smoothAnimationController) {
        const status = this.smoothAnimationController.getDeviceStatus('test_debug_car');
        console.log('1秒后队列状态:', status);
      }
    }, 1000);

    // 5秒后发送第二个数据点
    setTimeout(() => {
      const testData2 = [{
        device_id: 'test_debug_car',
        position: { x: 100, y: 0, z: 100 },
        rotation: { x: 0, y: 90, z: 0 },
        power: 75,
        duration: 1500 // 1.5秒动画
      }];

      console.log('发送第二个调试数据:', testData2[0]);
      this.handleVehicleControlMessage(testData2);
    }, 5000);
  }

  /**
   * 测试延时启动功能
   */
  testDelayedStart() {
    console.log('🧪 测试延时启动功能...');

    // 重置延时启动系统
    this.resetDelayedStartSystem();

    // 创建测试数据，模拟schedule_result.json的结构
    const testData = [
      // 设备0，立即启动
      {
        device_id: 0,
        start_time: 0,
        position: { x: 100, y: 0, z: 100 },
        rotation: { x: 0, y: 90, z: 0 },
        power: 100,
        duration: 2000
      },
      // 设备1，2秒后启动
      {
        device_id: 1,
        start_time: 2000,
        position: { x: 200, y: 0, z: 100 },
        rotation: { x: 0, y: 90, z: 0 },
        power: 95,
        duration: 2000
      },
      // 设备2，5秒后启动
      {
        device_id: 2,
        start_time: 5000,
        position: { x: 300, y: 0, z: 100 },
        rotation: { x: 0, y: 90, z: 0 },
        power: 90,
        duration: 2000
      }
    ];

    console.log('📋 测试数据:', testData);
    console.log('⏰ 预期行为:');
    console.log('  - 设备0: 立即开始移动');
    console.log('  - 设备1: 2秒后开始移动');
    console.log('  - 设备2: 5秒后开始移动');

    // 发送测试数据
    this.handleVehicleControlMessage(testData);

    // 定期显示延时启动状态
    const statusInterval = setInterval(() => {
      const status = this.getDelayedStartStatus();
      console.log('📊 延时启动状态:', status);

      // 如果没有活跃的定时器，停止状态监控
      if (status.activeTimers === 0) {
        clearInterval(statusInterval);
        console.log('✅ 所有设备已启动，测试完成');
      }
    }, 1000);

    // 10秒后清理
    setTimeout(() => {
      clearInterval(statusInterval);
      console.log('🧪 延时启动测试结束');
    }, 10000);
  }

  /**
   * 测试多设备延时启动
   */
  testMultiDeviceDelayedStart() {
    console.log('🧪 测试多设备延时启动...');

    // 重置延时启动系统
    this.resetDelayedStartSystem();

    // 模拟真实的电文数据结构
    const mockElectricMessage = [
      {
        device_id: 0,
        path: [
          {
            position: { x: 1200, y: 0, z: -435 },
            rotation: { x: 0, y: -90, z: 0 },
            duration: 1000,
            power: 100,
            consume: 0
          },
          {
            position: { x: 1180, y: 0, z: -435 },
            rotation: { x: 0, y: -90, z: 0 },
            duration: 2000,
            power: 95,
            consume: 5
          }
        ],
        start_time: 0
      },
      {
        device_id: 1,
        path: [
          {
            position: { x: 890, y: 0, z: -730 },
            rotation: { x: 0, y: -90, z: 0 },
            duration: 630,
            power: 100,
            consume: 0
          },
          {
            position: { x: 910, y: 0, z: -730 },
            rotation: { x: 0, y: -90, z: 0 },
            duration: 2000,
            power: 90,
            consume: 10
          }
        ],
        start_time: 3000 // 3秒后启动
      }
    ];

    console.log('📋 模拟电文数据:', mockElectricMessage);

    // 转换为RouteSimulator期望的格式
    const convertedData = mockElectricMessage.map(devicePath => {
      const { device_id, path, start_time } = devicePath;
      return path.map(pathPoint => ({
        device_id: device_id,
        start_time: start_time,
        position: pathPoint.position,
        rotation: pathPoint.rotation,
        duration: pathPoint.duration,
        power: pathPoint.power,
        consume: pathPoint.consume
      }));
    }).flat();

    console.log('🔄 转换后的数据:', convertedData);

    // 发送数据
    this.handleVehicleControlMessage(convertedData);

    console.log('⏰ 预期行为:');
    console.log('  - 设备0: 立即开始执行2个路径点');
    console.log('  - 设备1: 3秒后开始执行2个路径点');
  }

  /**
   * 测试基于duration的匀速动画
   */
  testQueueBuffer() {
    console.log('测试基于duration的匀速动画...');

    // 发送带有不同duration的数据点
    const testSequence = [
      { x: 0, y: 0, z: 0, duration: 1000 },     // 1秒到达
      { x: 30, y: 0, z: 0, duration: 1500 },    // 1.5秒到达
      { x: 60, y: 0, z: 0, duration: 1000 },    // 1秒到达
      { x: 90, y: 0, z: 0, duration: 2000 },    // 2秒到达
      { x: 120, y: 0, z: 0, duration: 1000 },   // 1秒到达
      { x: 150, y: 0, z: 0, duration: 1500 },   // 1.5秒到达
      { x: 180, y: 0, z: 0, duration: 1000 }    // 1秒到达
    ];

    console.log('测试说明: 验证基于duration的匀速动画');
    console.log('  - 每个数据点都有指定的duration');
    console.log('  - 延时2秒后开始处理');
    console.log('  - 根据duration进行精确的匀速插值');
    console.log('  - 一段动画完成后立即开始下一段');
    console.log('  - 实现真正的无缝衔接');

    testSequence.forEach((data, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_duration_car',
          position: { x: data.x, y: data.y, z: data.z },
          rotation: { x: 0, y: 90, z: 0 },
          power: 90 - index * 3,
          duration: data.duration
        }];

        console.log(`[${new Date().toLocaleTimeString()}] 发送duration动画数据 ${index + 1}: 目标=(${data.x}, ${data.y}, ${data.z}), 时长=${data.duration}ms`);
        this.handleVehicleControlMessage(testData);

        // 显示动画状态
        if (this.smoothAnimationController) {
          setTimeout(() => {
            const status = this.smoothAnimationController.getDeviceStatus('test_duration_car');
            console.log(`动画状态: 队列=${status.queueSize}, 动画中=${status.isAnimating}`);
          }, 100);
        }
      }, index * 300); // 每300ms发送一个数据点（比动画时间短，测试队列缓冲）
    });

    console.log('Duration动画测试启动，观察车辆是否按duration精确移动');
  }

  /**
   * 直接更新3D模型位置（跳过ModelComponent动画）
   */
  directUpdateModel3D(deviceId, position, rotation) {
    if (!this.sceneManager || !this.sceneManager.getModelRef) {
      console.warn(`⚠️ sceneManager 或 getModelRef 未初始化`);
      return;
    }

    // 获取模型引用
    const modelRef = this.sceneManager.getModelRef(deviceId);
    if (!modelRef || !modelRef.getModel) {
      console.warn(`⚠️ 未找到设备 ${deviceId} 的模型引用`);
      return;
    }

    // 获取Three.js模型对象
    const model3D = modelRef.getModel();
    if (!model3D) {
      console.warn(`⚠️ 设备 ${deviceId} 的3D模型对象为空`);
      return;
    }

    try {
      // 调试日志
      if (Math.random() < 0.01) { // 1%的概率输出日志
        console.log(`直接更新3D模型: ${deviceId}, 位置=(${position.x.toFixed(1)}, ${position.z.toFixed(1)})`);
      }

      // 直接设置3D模型的位置
      if (position) {
        model3D.position.set(
          position.x !== undefined ? position.x : model3D.position.x,
          position.y !== undefined ? position.y : model3D.position.y,
          position.z !== undefined ? position.z : model3D.position.z
        );
      }

      // 直接设置3D模型的旋转
      if (rotation) {
        model3D.rotation.set(
          rotation.x !== undefined ? (rotation.x * Math.PI / 180) : model3D.rotation.x,
          rotation.y !== undefined ? (rotation.y * Math.PI / 180) : model3D.rotation.y,
          rotation.z !== undefined ? (rotation.z * Math.PI / 180) : model3D.rotation.z
        );
      }

      // 更新ModelComponent的内部状态，保持同步
      if (modelRef.updateCurrentPosition && position) {
        modelRef.currentPosition = { ...position };
      }
      if (modelRef.updateCurrentRotation && rotation) {
        modelRef.currentRotation = { ...rotation };
      }

    } catch (error) {
      console.error(`❌ 直接更新模型 ${deviceId} 失败:`, error);
    }
  }

  /**
   * 同时旋转多个mesh（立即执行）
   */
  rotateMultipleMeshes(componentTurn) {
    if (!Array.isArray(componentTurn) || componentTurn.length === 0) {
      console.log('没有需要旋转的组件');
      return;
    }

    console.log(`开始同时旋转 ${componentTurn.length} 个mesh组件`);

    componentTurn.forEach((component, index) => {
      const { meshName, rotation } = component;
      console.log(`旋转组件 ${index + 1}: ${meshName}, Y轴角度: ${rotation.y}度`);
      this.rotateMeshByName(meshName, rotation.y);
    });
  }

  /**
   * 同时动画旋转多个mesh
   */
  animateMultipleMeshes(componentTurn, onComplete) {
    if (!Array.isArray(componentTurn) || componentTurn.length === 0) {
      console.log('没有需要动画旋转的组件');
      if (onComplete) onComplete();
      return;
    }

    console.log(`开始同时动画旋转 ${componentTurn.length} 个mesh组件`);

    let completedCount = 0;
    const totalCount = componentTurn.length;

    const checkAllComplete = () => {
      completedCount++;
      if (completedCount >= totalCount) {
        console.log('所有mesh组件旋转动画完成');
        if (onComplete) onComplete();
      }
    };

    componentTurn.forEach((component, index) => {
      const { meshName, rotation, duration } = component;
      console.log(`动画旋转组件 ${index + 1}: ${meshName}, Y轴角度: ${rotation.y}度, 时长: ${duration}ms`);

      this.animateMeshRotation(meshName, rotation.y, duration, checkAllComplete);
    });
  }

  /**
   * 销毁路线模拟器
   */
  destroy() {
    this.stop();
    this.disconnect();

    // 清理流畅动画控制器
    if (this.smoothAnimationController) {
      this.smoothAnimationController.clearAll();
    }

    this.model = null;
    this.routeData = [];
    console.log('RouteSimulator destroyed');
  }

  /**
   * 特殊条件下移动障碍物模型
   */
  moveObstacleOnSpecialCondition() {
    console.log('触发特殊条件：移动障碍物模型');

    // 获取场景中的所有障碍物
    if (window.sceneManagerRef && window.sceneManagerRef.getScene) {
      const scene = window.sceneManagerRef.getScene();

      // 查找障碍物模型
      const obstacles = scene.children.filter(child =>
        child.userData && child.userData.type === 'obstacle'
      );

      console.log(`找到 ${obstacles.length} 个障碍物模型`);

      obstacles.forEach((obstacle, index) => {
        console.log(`移动障碍物 ${index + 1}: ${obstacle.userData.name || 'Unknown'}`);

        // 起始位置
        const startPosition = { x: 680, y: 0, z: -430 };
        // 目标位置
        const endPosition = { x: 680, y: 0, z: -500 };

        // 设置起始位置
        obstacle.position.set(startPosition.x, startPosition.y, startPosition.z);

        // 创建动画移动到目标位置
        this.animateObstacle(obstacle, startPosition, endPosition, 20000); // 2秒动画
      });
    } else {
      console.warn('⚠️ 场景管理器未找到，无法移动障碍物');
    }
  }

  /**
   * 动画移动障碍物
   */
  animateObstacle(obstacle, startPos, endPos, duration) {
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 线性插值计算当前位置
      const currentPos = {
        x: startPos.x + (endPos.x - startPos.x) * progress,
        y: startPos.y + (endPos.y - startPos.y) * progress,
        z: startPos.z + (endPos.z - startPos.z) * progress
      };

      // 更新障碍物位置
      obstacle.position.set(currentPos.x, currentPos.y, currentPos.z);

      // 如果动画未完成，继续下一帧
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        console.log(`障碍物移动完成: (${endPos.x}, ${endPos.y}, ${endPos.z})`);
      }
    };

    console.log(`开始障碍物动画: (${startPos.x}, ${startPos.y}, ${startPos.z}) -> (${endPos.x}, ${endPos.y}, ${endPos.z})`);
    animate();
  }

  /**
   * 根据名称查找并旋转指定的mesh（以mesh中心点为轴）
   */
  rotateMeshByName(meshName, yRotationDegrees) {
    if (!this.model) {
      console.warn('Model not available for mesh rotation');
      return;
    }

    console.log(`旋转mesh: ${meshName}, Y轴角度: ${yRotationDegrees}度`);

    // 递归查找指定名称的mesh
    const targetMesh = this.findMeshByName(this.model, meshName);

    if (targetMesh) {
      // 计算mesh的中心点并以中心点为轴旋转
      this.rotateMeshAroundCenter(targetMesh, yRotationDegrees);
      console.log(`成功旋转mesh: ${meshName}`);
    } else {
      console.warn(`未找到名称为 "${meshName}" 的mesh`);
    }
  }

  /**
   * 动画旋转指定的mesh（以mesh中心点为轴）- 性能优化版本
   */
  animateMeshRotation(meshName, yRotationDegrees, duration, onComplete) {
    if (!this.model) {
      console.warn('Model not available for mesh rotation animation');
      if (onComplete) onComplete();
      return;
    }

    const targetMesh = this.findMeshByName(this.model, meshName);

    if (!targetMesh) {
      console.warn(`未找到名称为 "${meshName}" 的mesh，无法执行动画`);
      if (onComplete) onComplete();
      return;
    }

    // 确保输入的角度是标准化的
    const inputAngleNormalized = this.normalizeAngle(yRotationDegrees);

    // 获取当前旋转角度并标准化
    const currentRotationDegrees = this.normalizeAngle(this.getMeshCurrentRotation(targetMesh));

    // 计算最短旋转路径
    const rotationDiff = this.calculateShortestRotationPath(currentRotationDegrees, inputAngleNormalized);

    // 检查duration是否合理，如果太小则设置最小值
    const minDuration = 100; // 最小100ms
    const actualDuration = Math.max(duration, minDuration);

    // 计算旋转速度（度/秒）
    const rotationSpeed = Math.abs(rotationDiff) / (actualDuration / 1000);

    const startTime = performance.now();

    // 减少日志输出，只在调试模式下显示
    if (this.options.debugMode) {
      console.log(`🔄 动画旋转mesh: ${meshName}, ${currentRotationDegrees.toFixed(1)}° → ${inputAngleNormalized.toFixed(1)}°, ${actualDuration}ms`);

      // 如果旋转速度过快，给出警告
      if (rotationSpeed > 360) {
        console.warn(`⚠️ 旋转速度过快: ${rotationSpeed.toFixed(1)}°/s`);
      }
    }

    // 如果角度差值很小，直接设置最终角度
    if (Math.abs(rotationDiff) < 0.1) {
      this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
      if (onComplete) onComplete();
      return;
    }

    // 使用节流的动画循环，减少不必要的计算
    let lastUpdateTime = startTime;
    const updateInterval = 16; // 约60fps，减少到约60fps以提高性能

    const animate = (currentTime) => {
      if (!this.isRunning || this.isPaused) return;

      // 节流：只在间隔时间后才更新
      if (currentTime - lastUpdateTime < updateInterval) {
        requestAnimationFrame(animate);
        return;
      }
      lastUpdateTime = currentTime;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / actualDuration, 1);

      // 使用平滑缓动
      const easing = this.options.smoothRotation
        ? EasingFunctions.easeInOutQuad(progress)
        : progress;

      // 线性插值计算当前角度
      const currentAngle = currentRotationDegrees + rotationDiff * easing;

      // 以中心点为轴旋转
      this.rotateMeshAroundCenter(targetMesh, currentAngle);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 确保最终角度精确
        this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
        if (this.options.debugMode) {
          console.log(`✅ mesh旋转动画完成: ${meshName}`);
        }
        if (onComplete) onComplete();
      }
    };

    requestAnimationFrame(animate);
  }

  /**
   * 以mesh中心点为轴进行Y轴旋转（性能优化版本）
   */
  rotateMeshAroundCenter(mesh, yRotationDegrees) {
    if (!mesh) return;

    // 检查是否需要重新初始化旋转数据
    if (!mesh.userData.rotationData || this.shouldReinitializeRotationData(mesh)) {
      // 减少日志输出，只在开发模式下显示
      if (this.options.debugMode) {
        console.log(`重新初始化mesh "${mesh.name}" 的旋转数据`);
      }
      this.initializeMeshRotationData(mesh);
    }

    const rotationData = mesh.userData.rotationData;

    // 确保输入角度在合理范围内
    const normalizedDegrees = this.normalizeAngle(yRotationDegrees);
    const targetRotationRad = THREE.MathUtils.degToRad(normalizedDegrees);

    // 减少调试日志输出，只在必要时显示
    if (this.options.debugMode && Math.random() < 0.1) { // 10%概率输出日志
      console.log(`🔧 旋转mesh "${mesh.name}": ${normalizedDegrees.toFixed(1)}°`);
    }

    // 使用优化的旋转变换
    this.applyOptimizedRotationAroundPoint(mesh, rotationData.center, targetRotationRad);

    // 存储当前旋转角度（角度制）
    mesh.userData.currentRotationY = normalizedDegrees;
  }

  /**
   * 检查是否需要重新初始化旋转数据
   */
  shouldReinitializeRotationData(mesh) {
    if (!mesh.userData.rotationData) return true;

    const rotationData = mesh.userData.rotationData;

    // 检查原始位置是否发生变化（可能是由于模型重新加载或位置更新）
    const positionChanged = !mesh.position.equals(rotationData.originalPosition);

    if (positionChanged) {
      console.log(`检测到mesh "${mesh.name}" 位置发生变化，需要重新初始化旋转数据`);
      console.log(`- 当前位置: (${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)})`);
      console.log(`- 原始位置: (${rotationData.originalPosition.x.toFixed(2)}, ${rotationData.originalPosition.y.toFixed(2)}, ${rotationData.originalPosition.z.toFixed(2)})`);
      return true;
    }

    return false;
  }

  /**
   * 围绕指定点进行Y轴旋转（性能优化版本）
   * @param {Object} mesh 要旋转的mesh
   * @param {Vector3} centerPoint 旋转中心点
   * @param {number} targetRotationRad 目标旋转角度（弧度制，绝对角度）
   */
  applyOptimizedRotationAroundPoint(mesh, centerPoint, targetRotationRad) {
    if (!mesh || !centerPoint) return;

    const rotationData = mesh.userData.rotationData;
    if (!rotationData) return;

    // 重置到原始状态
    mesh.position.copy(rotationData.originalPosition);
    mesh.rotation.copy(rotationData.originalRotation);
    mesh.scale.copy(rotationData.originalScale);

    // 计算旋转增量（目标角度 - 原始角度）
    const rotationDelta = targetRotationRad - rotationData.originalRotation.y;

    // 减少调试日志输出
    if (this.options.debugMode && Math.random() < 0.05) { // 5%概率输出日志
      console.log(`🔧 旋转变换: 增量=${THREE.MathUtils.radToDeg(rotationDelta).toFixed(1)}°`);
    }

    // 使用缓存的相对位置和旋转矩阵（如果可能）
    if (!rotationData.cachedRelativePosition) {
      rotationData.cachedRelativePosition = rotationData.originalPosition.clone().sub(centerPoint);
    }

    // 复用相对位置向量，避免重复创建
    const relativePosition = rotationData.cachedRelativePosition.clone();

    // 使用缓存的旋转矩阵或创建新的
    if (!rotationData.rotationMatrix) {
      rotationData.rotationMatrix = new THREE.Matrix4();
    }
    rotationData.rotationMatrix.makeRotationY(rotationDelta);

    // 应用旋转到相对位置
    relativePosition.applyMatrix4(rotationData.rotationMatrix);

    // 设置新的位置
    mesh.position.copy(centerPoint).add(relativePosition);

    // 设置新的旋转（直接使用目标旋转角度）
    mesh.rotation.y = targetRotationRad;
  }

  /**
   * 初始化mesh的旋转数据（性能优化版本）
   */
  initializeMeshRotationData(mesh) {
    if (!mesh) return;

    // 确保mesh的矩阵是最新的
    mesh.updateMatrixWorld(true);

    // 计算单个mesh的几何中心点（基于当前状态，这是原始状态）
    const center = this.calculateMeshGeometryCenter(mesh);

    // 存储原始变换矩阵和中心点，添加性能优化的缓存
    mesh.userData.rotationData = {
      originalMatrix: mesh.matrix.clone(),
      center: center.clone(),  // 这是基于原始状态计算的中心点
      originalPosition: mesh.position.clone(),
      originalRotation: mesh.rotation.clone(),
      originalScale: mesh.scale.clone(),
      originalWorldMatrix: mesh.matrixWorld.clone(),  // 保存原始世界矩阵

      // 性能优化：缓存常用的计算结果
      cachedRelativePosition: null,  // 缓存相对位置向量
      rotationMatrix: null,  // 缓存旋转矩阵，避免重复创建
      lastUpdateTime: performance.now()  // 记录最后更新时间
    };

    // 初始化当前旋转角度
    mesh.userData.currentRotationY = THREE.MathUtils.radToDeg(mesh.rotation.y);

    // 减少日志输出，只在调试模式下显示
    if (this.options.debugMode) {
      console.log(`初始化mesh "${mesh.name}" 旋转数据: 中心(${center.x.toFixed(1)}, ${center.y.toFixed(1)}, ${center.z.toFixed(1)})`);
    }
  }

  /**
   * 计算mesh或子模型集合的真实几何中心点 - 修复版本
   */
  calculateMeshGeometryCenter(mesh) {
    if (!mesh) return new THREE.Vector3();

    // 如果是单个Mesh对象且有geometry
    if (mesh.isMesh && mesh.geometry) {
      return this.calculateSingleMeshCenter(mesh);
    }

    // 如果是Group或包含子对象的复合模型，计算整个集合的真实中心点
    if (mesh.children && mesh.children.length > 0) {
      return this.calculateModelCollectionCenter(mesh);
    }

    // 备用方案：使用对象的位置作为中心点
    if (this.options.debugMode) {
      console.log(`mesh "${mesh.name}" 使用位置作为中心点: (${mesh.position.x.toFixed(1)}, ${mesh.position.y.toFixed(1)}, ${mesh.position.z.toFixed(1)})`);
    }
    return mesh.position.clone();
  }

  /**
   * 计算单个mesh的几何中心点
   */
  calculateSingleMeshCenter(mesh) {
    if (!mesh.isMesh || !mesh.geometry) return new THREE.Vector3();

    // 检查是否已经计算过包围盒，避免重复计算
    if (!mesh.geometry.boundingBox) {
      mesh.geometry.computeBoundingBox();
    }

    if (!mesh.geometry.boundingBox) return mesh.position.clone();

    const center = new THREE.Vector3();
    mesh.geometry.boundingBox.getCenter(center);

    // 使用缓存的变换矩阵或创建新的
    if (!mesh.userData.tempMatrix) {
      mesh.userData.tempMatrix = new THREE.Matrix4();
      mesh.userData.tempQuaternion = new THREE.Quaternion();
    }

    mesh.userData.tempQuaternion.setFromEuler(mesh.rotation);
    mesh.userData.tempMatrix.compose(
      mesh.position,
      mesh.userData.tempQuaternion,
      mesh.scale
    );

    // 将几何中心转换到世界坐标系
    center.applyMatrix4(mesh.userData.tempMatrix);

    if (this.options.debugMode) {
      console.log(`单个mesh "${mesh.name}" 几何中心: (${center.x.toFixed(1)}, ${center.y.toFixed(1)}, ${center.z.toFixed(1)})`);
    }
    return center;
  }

  /**
   * 计算子模型集合的真实几何中心点 - 基于整体包围盒
   */
  calculateModelCollectionCenter(parentMesh) {
    if (!parentMesh || !parentMesh.children || parentMesh.children.length === 0) {
      return parentMesh ? parentMesh.position.clone() : new THREE.Vector3();
    }

    // 使用缓存避免重复遍历和计算
    if (!parentMesh.userData.cachedCollectionData || this.shouldRecalculateCollectionCenter(parentMesh)) {
      this.calculateAndCacheCollectionData(parentMesh);
    }

    const cachedData = parentMesh.userData.cachedCollectionData;
    if (cachedData && cachedData.center) {
      if (this.options.debugMode) {
        console.log(`子模型集合 "${parentMesh.name}" 中心点: (${cachedData.center.x.toFixed(1)}, ${cachedData.center.y.toFixed(1)}, ${cachedData.center.z.toFixed(1)}), ${cachedData.meshCount}个子mesh`);
      }
      return cachedData.center.clone();
    }

    // 备用方案
    return parentMesh.position.clone();
  }

  /**
   * 计算并缓存子模型集合的数据
   */
  calculateAndCacheCollectionData(parentMesh) {
    const allMeshes = [];
    const boundingBoxes = [];

    // 遍历所有子对象，收集mesh和它们的世界包围盒
    parentMesh.traverse((child) => {
      if (child.isMesh && child.geometry) {
        allMeshes.push(child);

        // 确保几何体有包围盒
        if (!child.geometry.boundingBox) {
          child.geometry.computeBoundingBox();
        }

        if (child.geometry.boundingBox) {
          // 计算世界空间的包围盒
          const worldBoundingBox = this.calculateWorldBoundingBox(child);
          if (worldBoundingBox) {
            boundingBoxes.push(worldBoundingBox);
          }
        }
      }
    });

    if (boundingBoxes.length === 0) {
      // 如果没有有效的包围盒，使用父对象位置
      parentMesh.userData.cachedCollectionData = {
        center: parentMesh.position.clone(),
        meshCount: 0,
        lastUpdateTime: performance.now()
      };
      return;
    }

    // 计算整个集合的总包围盒
    const totalBoundingBox = new THREE.Box3();
    boundingBoxes.forEach(box => {
      totalBoundingBox.union(box);
    });

    // 获取总包围盒的中心点
    const center = new THREE.Vector3();
    totalBoundingBox.getCenter(center);

    // 缓存计算结果
    parentMesh.userData.cachedCollectionData = {
      center: center,
      meshCount: allMeshes.length,
      totalBoundingBox: totalBoundingBox,
      lastUpdateTime: performance.now()
    };

    if (this.options.debugMode) {
      console.log(`计算子模型集合 "${parentMesh.name}" 真实中心点:`);
      console.log(`- 包含 ${allMeshes.length} 个子mesh`);
      console.log(`- 总包围盒: min(${totalBoundingBox.min.x.toFixed(1)}, ${totalBoundingBox.min.y.toFixed(1)}, ${totalBoundingBox.min.z.toFixed(1)})`);
      console.log(`- 总包围盒: max(${totalBoundingBox.max.x.toFixed(1)}, ${totalBoundingBox.max.y.toFixed(1)}, ${totalBoundingBox.max.z.toFixed(1)})`);
      console.log(`- 真实中心: (${center.x.toFixed(1)}, ${center.y.toFixed(1)}, ${center.z.toFixed(1)})`);
    }
  }

  /**
   * 计算mesh在世界空间中的包围盒
   */
  calculateWorldBoundingBox(mesh) {
    if (!mesh.isMesh || !mesh.geometry || !mesh.geometry.boundingBox) {
      return null;
    }

    // 获取本地包围盒
    const localBoundingBox = mesh.geometry.boundingBox.clone();

    // 确保mesh的世界矩阵是最新的
    mesh.updateMatrixWorld(true);

    // 将本地包围盒转换为世界空间
    const worldBoundingBox = localBoundingBox.clone();
    worldBoundingBox.applyMatrix4(mesh.matrixWorld);

    return worldBoundingBox;
  }

  /**
   * 检查是否需要重新计算子模型集合的中心点
   */
  shouldRecalculateCollectionCenter(parentMesh) {
    if (!parentMesh.userData.cachedCollectionData) {
      return true;
    }

    const cachedData = parentMesh.userData.cachedCollectionData;
    const currentTime = performance.now();

    // 如果缓存时间超过5秒，重新计算
    if (currentTime - cachedData.lastUpdateTime > 5000) {
      return true;
    }

    // 检查子对象数量是否发生变化
    let currentMeshCount = 0;
    parentMesh.traverse((child) => {
      if (child.isMesh && child.geometry) {
        currentMeshCount++;
      }
    });

    if (currentMeshCount !== cachedData.meshCount) {
      if (this.options.debugMode) {
        console.log(`子模型集合 "${parentMesh.name}" 的mesh数量发生变化: ${cachedData.meshCount} -> ${currentMeshCount}`);
      }
      return true;
    }

    return false;
  }

  /**
   * 获取mesh当前的Y轴旋转角度（度数）
   */
  getMeshCurrentRotation(mesh) {
    if (!mesh) return 0;

    let currentAngle = 0;

    // 优先使用存储的角度，否则从当前旋转值转换
    if (mesh.userData && mesh.userData.currentRotationY !== undefined) {
      currentAngle = mesh.userData.currentRotationY;
    } else {
      currentAngle = THREE.MathUtils.radToDeg(mesh.rotation.y);
    }

    // 返回标准化的角度
    return this.normalizeAngle(currentAngle);
  }

  /**
   * 递归查找指定名称的mesh
   */
  findMeshByName(object, name) {
    console.log(`查找mesh: "${name}"`);

    // 首先打印模型的层级结构，便于调试
    this.printModelHierarchy(object, 0);

    const found = this.searchMeshByName(object, name);

    if (found) {
      console.log(`找到mesh: "${name}", 类型: ${found.type}, 是否为Mesh: ${found.isMesh}`);
      if (found.geometry) {
        console.log(`- 几何体类型: ${found.geometry.type}`);
        console.log(`- 顶点数: ${found.geometry.attributes.position ? found.geometry.attributes.position.count : 'N/A'}`);
      }
    } else {
      console.warn(`未找到名称为 "${name}" 的mesh`);
    }

    return found;
  }

  /**
   * 递归搜索指定名称的mesh
   */
  searchMeshByName(object, name) {
    if (object.name === name) {
      return object;
    }

    if (object.children) {
      for (let child of object.children) {
        const found = this.searchMeshByName(child, name);
        if (found) {
          return found;
        }
      }
    }

    return null;
  }

  /**
   * 打印模型的层级结构（用于调试子模型集合中心点问题）
   */
  printModelHierarchy(object, depth = 0) {
    const indent = '  '.repeat(depth);
    const type = object.isMesh ? 'Mesh' : object.isGroup ? 'Group' : object.type || 'Object3D';
    const hasGeometry = object.geometry ? ' [有几何体]' : '';
    const position = `pos:(${object.position.x.toFixed(1)}, ${object.position.y.toFixed(1)}, ${object.position.z.toFixed(1)})`;

    // 添加包围盒信息（如果有几何体）
    let boundingBoxInfo = '';
    if (object.isMesh && object.geometry) {
      if (!object.geometry.boundingBox) {
        object.geometry.computeBoundingBox();
      }
      if (object.geometry.boundingBox) {
        const box = object.geometry.boundingBox;
        const size = {
          x: (box.max.x - box.min.x).toFixed(1),
          y: (box.max.y - box.min.y).toFixed(1),
          z: (box.max.z - box.min.z).toFixed(1)
        };
        boundingBoxInfo = ` size:(${size.x}×${size.y}×${size.z})`;
      }
    }

    // 添加子对象数量信息
    const childrenInfo = object.children && object.children.length > 0 ? ` [${object.children.length}个子对象]` : '';

    // 添加缓存状态信息
    let cacheInfo = '';
    if (object.userData) {
      const hasRotationData = object.userData.rotationData ? ' [有旋转缓存]' : '';
      const hasCollectionData = object.userData.cachedCollectionData ? ' [有集合缓存]' : '';
      cacheInfo = hasRotationData + hasCollectionData;
    }

    console.log(`${indent}${type}: "${object.name}"${hasGeometry}${boundingBoxInfo} ${position}${childrenInfo}${cacheInfo}`);

    if (object.children && object.children.length > 0) {
      object.children.forEach(child => {
        this.printModelHierarchy(child, depth + 1);
      });
    }
  }

  /**
   * 打印指定对象的中心点计算详情（调试用）
   */
  printCenterCalculationDetails(object) {
    if (!object) {
      console.warn('对象为空，无法计算中心点详情');
      return;
    }

    console.log(`\n=== 中心点计算详情: "${object.name}" ===`);
    console.log(`对象类型: ${object.isMesh ? 'Mesh' : object.isGroup ? 'Group' : object.type || 'Object3D'}`);
    console.log(`对象位置: (${object.position.x.toFixed(2)}, ${object.position.y.toFixed(2)}, ${object.position.z.toFixed(2)})`);

    if (object.isMesh && object.geometry) {
      // 单个mesh的中心点计算
      const center = this.calculateSingleMeshCenter(object);
      console.log(`单个mesh几何中心: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);
    } else if (object.children && object.children.length > 0) {
      // 子模型集合的中心点计算
      console.log(`子对象数量: ${object.children.length}`);

      let meshCount = 0;
      object.traverse(child => {
        if (child.isMesh && child.geometry) meshCount++;
      });
      console.log(`包含的mesh数量: ${meshCount}`);

      const center = this.calculateModelCollectionCenter(object);
      console.log(`子模型集合真实中心: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);

      // 显示缓存状态
      if (object.userData.cachedCollectionData) {
        const cache = object.userData.cachedCollectionData;
        console.log(`缓存状态: 已缓存 (${cache.meshCount}个mesh, ${((performance.now() - cache.lastUpdateTime) / 1000).toFixed(1)}秒前)`);
      } else {
        console.log(`缓存状态: 未缓存`);
      }
    } else {
      console.log(`使用对象位置作为中心点: (${object.position.x.toFixed(2)}, ${object.position.y.toFixed(2)}, ${object.position.z.toFixed(2)})`);
    }
    console.log(`=== 中心点计算详情结束 ===\n`);
  }
}

export default RouteSimulator;
