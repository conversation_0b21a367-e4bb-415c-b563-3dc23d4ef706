// 地面偏移量计算和应用的测试脚本
// 用于验证修复是否正确

console.log('=== 地面偏移量计算测试 ===');

// 模拟不同的模型边界框情况
const testCases = [
  {
    name: '模型底部在原点之下',
    boundingBox: { min: { y: -6.2 }, max: { y: 3.0 } },
    configY: 0,
    description: '典型情况：模型底部需要向上移动6.2个单位才能贴紧地面'
  },
  {
    name: '模型底部在原点之上',
    boundingBox: { min: { y: 2.0 }, max: { y: 8.0 } },
    configY: 0,
    description: 'Bug情况：模型底部需要向下移动2.0个单位才能贴紧地面'
  },
  {
    name: '模型底部正好在原点',
    boundingBox: { min: { y: 0.0 }, max: { y: 5.0 } },
    configY: 0,
    description: '边界情况：模型底部已经在地面上'
  },
  {
    name: '模型底部在原点之下，配置Y=5',
    boundingBox: { min: { y: -3.0 }, max: { y: 2.0 } },
    configY: 5,
    description: '复合情况：模型底部距离地面5个单位'
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
  console.log(`描述: ${testCase.description}`);
  console.log(`边界框: min.y = ${testCase.boundingBox.min.y}, max.y = ${testCase.boundingBox.max.y}`);
  console.log(`配置Y坐标: ${testCase.configY}`);
  
  // 计算地面偏移量
  const groundOffset = -testCase.boundingBox.min.y;
  console.log(`地面偏移量: groundOffset = -box.min.y = -${testCase.boundingBox.min.y} = ${groundOffset}`);
  
  // 应用修复前的逻辑（有bug）
  let yPositionOld = testCase.configY;
  const shouldApplyOld = groundOffset > 0;
  if (shouldApplyOld) {
    yPositionOld = testCase.configY + groundOffset;
  }
  console.log(`修复前逻辑: groundOffset > 0 = ${shouldApplyOld}, 最终Y = ${yPositionOld}`);
  
  // 应用修复后的逻辑
  let yPositionNew = testCase.configY;
  const shouldApplyNew = true; // 总是应用（如果autoGroundPosition !== false）
  if (shouldApplyNew) {
    yPositionNew = testCase.configY + groundOffset;
  }
  console.log(`修复后逻辑: 总是应用偏移, 最终Y = ${yPositionNew}`);
  
  // 验证结果
  const modelBottomY = yPositionNew + testCase.boundingBox.min.y;
  const expectedGroundY = testCase.configY;
  console.log(`验证: 模型底部Y坐标 = ${yPositionNew} + ${testCase.boundingBox.min.y} = ${modelBottomY}`);
  console.log(`预期: 模型底部应该在Y = ${expectedGroundY}`);
  console.log(`结果: ${Math.abs(modelBottomY - expectedGroundY) < 0.001 ? '✅ 正确' : '❌ 错误'}`);
  
  if (yPositionOld !== yPositionNew) {
    console.log(`🔧 修复生效: 从 Y=${yPositionOld} 修正为 Y=${yPositionNew}`);
  }
});

console.log('\n=== 总结 ===');
console.log('修复内容:');
console.log('1. 移除了 groundOffset.value > 0 的条件限制');
console.log('2. 现在无论地面偏移量是正数、负数还是零，都会正确应用');
console.log('3. 确保模型底部始终贴紧配置的地面高度');

console.log('\n关键修改:');
console.log('// 修复前');
console.log('if (config.autoGroundPosition !== false && groundOffset.value > 0) {');
console.log('  yPosition = yPosition + groundOffset.value;');
console.log('}');
console.log('');
console.log('// 修复后');
console.log('if (config.autoGroundPosition !== false) {');
console.log('  yPosition = yPosition + groundOffset.value;');
console.log('}');
