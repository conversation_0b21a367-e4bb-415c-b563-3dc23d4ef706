# server
server.port=8080

# datasource
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;MODE=MYSQL;DATABASE_TO_LOWER=TRUE
spring.h2.console.enabled=true

# mybatis
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.mapper-locations=classpath:mapper/**/*.xml

# jpa
spring.jpa.open-in-view=false

# threads
spring.threads.virtual.enabled=true

# logging
logging.charset.console=UTF-8

# UDP configuration
udp.server.port=9090
udp.server.buffer.size=10240
udp.client.local.host=127.0.0.1
udp.client.local.port=8099
udp.client.target.host=127.0.0.1
udp.client.target.port=5000
udp.client.enabled=true
udp.broadcast.port=9091
udp.broadcast.address=127.0.0.1
udp.broadcast.enabled=false
